import { z } from "zod";
import { router, publicProcedure, protectedProcedure } from "../lib/trpc";
import prisma from "../../prisma";
import { TRPCError } from "@trpc/server";

// Input validation schemas
const createProductSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  slug: z.string().min(1, "Slug is required"),
});

const updateProductSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Product name is required").optional(),
  description: z.string().optional(),
  slug: z.string().min(1, "Slug is required").optional(),
});

const productIdSchema = z.object({
  id: z.string(),
});

const productSlugSchema = z.object({
  slug: z.string(),
});

const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
});

export const productRouter = router({
  // Get all products with pagination and search
  getAll: publicProcedure
    .input(paginationSchema)
    .query(async ({ input }) => {
      const { page, limit, search } = input;
      const skip = (page - 1) * limit;

      const where = search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" as const } },
              { description: { contains: search, mode: "insensitive" as const } },
            ],
          }
        : {};

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          skip,
          take: limit,
          include: {
            variants: {
              include: {
                stock: {
                  select: {
                    id: true,
                    status: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }),
        prisma.product.count({ where }),
      ]);

      return {
        products,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // Get product by ID
  getById: publicProcedure
    .input(productIdSchema)
    .query(async ({ input }) => {
      const product = await prisma.product.findUnique({
        where: { id: input.id },
        include: {
          variants: {
            include: {
              stock: {
                select: {
                  id: true,
                  status: true,
                },
              },
            },
          },
        },
      });

      if (!product) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Product not found",
        });
      }

      return product;
    }),

  // Get product by slug
  getBySlug: publicProcedure
    .input(productSlugSchema)
    .query(async ({ input }) => {
      const product = await prisma.product.findUnique({
        where: { slug: input.slug },
        include: {
          variants: {
            include: {
              stock: {
                select: {
                  id: true,
                  status: true,
                },
              },
            },
          },
        },
      });

      if (!product) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Product not found",
        });
      }

      return product;
    }),

  // Create new product (protected)
  create: protectedProcedure
    .input(createProductSchema)
    .mutation(async ({ input }) => {
      try {
        const product = await prisma.product.create({
          data: {
            ...input,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          include: {
            variants: true,
          },
        });

        return product;
      } catch (error: any) {
        if (error.code === "P2002") {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Product with this slug already exists",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create product",
        });
      }
    }),

  // Update product (protected)
  update: protectedProcedure
    .input(updateProductSchema)
    .mutation(async ({ input }) => {
      const { id, ...updateData } = input;

      try {
        const product = await prisma.product.update({
          where: { id },
          data: {
            ...updateData,
            updatedAt: new Date(),
          },
          include: {
            variants: true,
          },
        });

        return product;
      } catch (error: any) {
        if (error.code === "P2002") {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Product with this slug already exists",
          });
        }
        if (error.code === "P2025") {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Product not found",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update product",
        });
      }
    }),

  // Delete product (protected)
  delete: protectedProcedure
    .input(productIdSchema)
    .mutation(async ({ input }) => {
      try {
        await prisma.product.delete({
          where: { id: input.id },
        });

        return { success: true };
      } catch (error: any) {
        if (error.code === "P2025") {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Product not found",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete product",
        });
      }
    }),

  // Get product statistics (protected)
  getStats: protectedProcedure.query(async () => {
    const [totalProducts, totalVariants, totalStock] = await Promise.all([
      prisma.product.count(),
      prisma.variant.count(),
      prisma.stock.count(),
    ]);

    const stockByStatus = await prisma.stock.groupBy({
      by: ["status"],
      _count: {
        id: true,
      },
    });

    return {
      totalProducts,
      totalVariants,
      totalStock,
      stockByStatus: stockByStatus.reduce(
        (acc, item) => {
          acc[item.status] = item._count.id;
          return acc;
        },
        {} as Record<string, number>
      ),
    };
  }),
});
