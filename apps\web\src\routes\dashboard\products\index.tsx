import Loader from "@/components/loader";
import PageHeader from "@/components/pages/page-header";
import { authClient } from "@/lib/auth-client";
import { createFileRoute } from "@tanstack/react-router";
import { useEffect } from "react";
import ProductsTable from "@/components/products/products-table";

export const Route = createFileRoute("/dashboard/products/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { data: session, isPending } = authClient.useSession();

  const navigate = Route.useNavigate();

  useEffect(() => {
    if (!session && !isPending) {
      navigate({
        to: "/login",
      });
    }
  }, [session, isPending]);

  if (isPending) {
    return <Loader />;
  }

  return (
    <div className="space-y-6">
      <PageHeader title="Products" description="Manage all your products." />
      <ProductsTable />
    </div>
  );
}
