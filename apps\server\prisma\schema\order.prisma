enum OrderStatus {
    PENDING
    COMPLETED
    REFUNDED
    CANCELLED
}

model Order {
    id                   String      @id @default(uuid())
    invoiceNumber        String      @unique
    quantity             Int         @default(1)
    status               OrderStatus @default(PENDING)
    buyerEmail           String
    buyerWhatsapp        String
    deliveredCredentials Json[]

    variantId String
    variant   Variant @relation(fields: [variantId], references: [id])
    stocks Stock[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("orders")
}
