import { Link } from "@tanstack/react-router";
import { BarChart3, Home } from "lucide-react";

import { ModeToggle } from "./mode-toggle";
import UserMenu from "./user-menu";
import { Button } from "./ui/button";

export default function Header() {
  const links = [
    { to: "/dashboard", label: "Dashboard", icon: Home },
  ];

  return (
    <div>
      <div className="flex flex-row items-center justify-between px-2 py-3">
        <nav className="flex gap-2 text-lg px-4">
          {links.map(({ to, label, icon: Icon }) => {
            return (
              <Button
                key={to}
                variant="ghost"
                className="text-md gap-2"
                asChild
              >
                <Link to={to}>
                  <Icon className="h-4 w-4" />
                  {label}
                </Link>
              </Button>
            );
          })}
        </nav>
        <div className="flex items-center gap-2 px-4">
          <ModeToggle />
          <UserMenu />
        </div>
      </div>
      <hr />
    </div>
  );
}
