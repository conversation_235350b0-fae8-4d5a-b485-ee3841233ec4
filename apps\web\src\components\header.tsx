import { Link } from "@tanstack/react-router";
import {
  BarChart3,
  <PERSON><PERSON>s,
  Users,
  FileText,
  Home,
  ChevronDown,
  Bell,
  HelpCircle,
} from "lucide-react";

import { ModeToggle } from "./mode-toggle";
import UserMenu from "./user-menu";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";

// Types for navigation items
type NavigationSubItem = {
  title: string;
  href: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
};

type NavigationItem = {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  submenu?: NavigationSubItem[];
};

// Define navigation structure with and without submenus
const navigationItems: NavigationItem[] = [
  {
    title: "Home",
    href: "/",
    icon: Home,
    // No submenu - this will render as a simple link
  },
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: BarChart3,
    submenu: [
      {
        title: "Overview",
        href: "/dashboard",
        description: "Dashboard overview and analytics",
        icon: BarChart3,
      },
      {
        title: "Analytics",
        href: "/dashboard/analytics",
        description: "Detailed analytics and reports",
        icon: BarChart3,
      },
      {
        title: "Reports",
        href: "/dashboard/reports",
        description: "Generate and view reports",
        icon: FileText,
      },
    ],
  },
  {
    title: "Users",
    href: "/users",
    icon: Users,
    submenu: [
      {
        title: "All Users",
        href: "/users",
        description: "View and manage all users",
        icon: Users,
      },
      {
        title: "User Roles",
        href: "/users/roles",
        description: "Manage user roles and permissions",
        icon: Settings,
      },
    ],
  },
  {
    title: "Notifications",
    href: "/notifications",
    icon: Bell,
    // No submenu - simple link item
  },
  {
    title: "Help",
    href: "/help",
    icon: HelpCircle,
    // No submenu - simple link item
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
    submenu: [
      {
        title: "General",
        href: "/settings",
        description: "General application settings",
        icon: Settings,
      },
      {
        title: "Security",
        href: "/settings/security",
        description: "Security and privacy settings",
        icon: Settings,
      },
    ],
  },
];

export default function Header() {
  return (
    <div>
      <div className="flex flex-row items-center justify-between px-4 py-3">
        <NavigationMenu>
          <NavigationMenuList>
            {navigationItems.map((item) => (
              <NavigationMenuItem key={item.title}>
                {/* Render items with submenus as dropdown triggers */}
                {item.submenu && item.submenu.length > 0 ? (
                  <>
                    <NavigationMenuTrigger className="gap-2">
                      <item.icon className="h-4 w-4" />
                      {item.title}
                    </NavigationMenuTrigger>
                    <NavigationMenuContent>
                      <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                        {item.submenu.map((subItem) => (
                          <li key={subItem.title}>
                            <NavigationMenuLink asChild>
                              <Link
                                to={subItem.href}
                                className={cn(
                                  "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                )}
                              >
                                <div className="flex items-center gap-2">
                                  <subItem.icon className="h-4 w-4" />
                                  <div className="text-sm font-medium leading-none">
                                    {subItem.title}
                                  </div>
                                </div>
                                <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                                  {subItem.description}
                                </p>
                              </Link>
                            </NavigationMenuLink>
                          </li>
                        ))}
                      </ul>
                    </NavigationMenuContent>
                  </>
                ) : (
                  /* Render items without submenus as simple links */
                  <NavigationMenuLink asChild>
                    <Link
                      to={item.href}
                      className={cn(navigationMenuTriggerStyle(), "gap-2")}
                    >
                      <item.icon className="h-4 w-4" />
                      {item.title}
                    </Link>
                  </NavigationMenuLink>
                )}
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        <div className="flex items-center gap-2">
          <ModeToggle />
          <UserMenu />
        </div>
      </div>
      <hr />
    </div>
  );
}
