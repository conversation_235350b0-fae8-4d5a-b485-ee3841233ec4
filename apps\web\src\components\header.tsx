import { Link } from "@tanstack/react-router";

import { ModeToggle } from "./mode-toggle";
import UserMenu from "./user-menu";
import { Button } from "./ui/button";

export default function Header() {
  const links = [
    { to: "/", label: "Home" },
    { to: "/dashboard", label: "Dashboard" },
  ];

  return (
    <div>
      <div className="flex flex-row items-center justify-between px-2 py-3">
        <nav className="flex gap-2 text-lg px-4">
          {links.map(({ to, label }) => {
            return (
              <Button key={to} variant="ghost" asChild>
                <Link to={to} className="text-bold text-2xl">{label}</Link>
              </Button>
            );
          })}
        </nav>  
        <div className="flex items-center gap-2 px-4">
          <ModeToggle />
          <UserMenu />
        </div>
      </div>
      <hr />
    </div>
  );
}
