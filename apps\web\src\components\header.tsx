import { Link, useRouterState } from "@tanstack/react-router";
import { BarChart3, Home, ShoppingBag, Users, Menu, X } from "lucide-react";
import { useState } from "react";

import { ModeToggle } from "./mode-toggle";
import UserMenu from "./user-menu";
import { Button } from "./ui/button";
import { cn } from "@/lib/utils";

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const currentPath = useRouterState({
    select: (state) => state.location.pathname,
  });

  const links = [
    { to: "/dashboard", label: "Dashboard", icon: Home },
    { to: "/dashboard/products", label: "Products", icon: ShoppingBag },
  ];

  const isActive = (path: string) => {
    // Exact match for all paths to avoid conflicts
    if (path === "/") {
      return currentPath === "/";
    }

    // For other paths, check for exact match or if it's a direct child
    // but not a grandchild to avoid conflicts between /dashboard and /dashboard/products
    if (currentPath === path) {
      return true;
    }

    // Only consider it active if it's a direct child (one level deeper)
    const pathSegments = path.split("/").filter(Boolean);
    const currentSegments = currentPath.split("/").filter(Boolean);

    // If current path has exactly one more segment than the nav path,
    // and all nav path segments match, then it's a direct child
    if (currentSegments.length === pathSegments.length + 1) {
      return pathSegments.every(
        (segment, index) => segment === currentSegments[index]
      );
    }

    return false;
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <div>
      <div className="flex flex-row items-center justify-between px-2 py-4">
        {/* Left side - App name and desktop navigation */}
        <div className="flex items-center gap-6">
          <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight first:mt-0 pl-4">
            Bokul
          </h2>
          <div className="hidden md:block h-6 w-px bg-border"></div>
          <nav className="hidden md:flex gap-2 text-lg">
            {links.map(({ to, label, icon: Icon }) => {
              const active = isActive(to);
              return (
                <Button
                  key={to}
                  variant="ghost"
                  className={cn(
                    "text-sm gap-2",
                    active && "bg-accent text-accent-foreground"
                  )}
                  asChild
                >
                  <Link to={to}>
                    <Icon className="h-4 w-4" />
                    {label}
                  </Link>
                </Button>
              );
            })}
          </nav>
        </div>

        {/* Right side - Controls */}
        <div className="flex items-center gap-2 px-4">
          <div className="hidden md:flex items-center gap-2">
            <ModeToggle />
            <UserMenu />
          </div>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={toggleMobileMenu}
          >
            {isMobileMenuOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </Button>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden border-t bg-background">
          <nav className="flex flex-col p-4 space-y-2">
            {links.map(({ to, label, icon: Icon }) => {
              const active = isActive(to);
              return (
                <Button
                  key={to}
                  variant="ghost"
                  className={cn(
                    "justify-start text-sm gap-2 w-full",
                    active && "bg-accent text-accent-foreground"
                  )}
                  asChild
                  onClick={closeMobileMenu}
                >
                  <Link to={to}>
                    <Icon className="h-4 w-4" />
                    {label}
                  </Link>
                </Button>
              );
            })}
            <div className="flex items-center gap-2 pt-4 border-t">
              <ModeToggle />
              <UserMenu />
            </div>
          </nav>
        </div>
      )}

      <hr />
    </div>
  );
}
