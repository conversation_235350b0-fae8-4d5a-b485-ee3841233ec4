import { <PERSON>, useRouter<PERSON>tate } from "@tanstack/react-router";
import { BarChart3, Home, ShoppingBag, Users } from "lucide-react";

import { ModeToggle } from "./mode-toggle";
import UserMenu from "./user-menu";
import { Button } from "./ui/button";
import { cn } from "@/lib/utils";

export default function Header() {
  const currentPath = useRouterState({
    select: (state) => state.location.pathname,
  });

  const links = [
    { to: "/dashboard", label: "Dashboard", icon: Home },
    { to: "/users", label: "Products", icon: ShoppingBag },
  ];

  const isActive = (path: string) => {
    // Exact match for root path, starts with for other paths
    if (path === "/") {
      return currentPath === "/";
    }
    return currentPath.startsWith(path);
  };

  return (
    <div>
      <div className="flex flex-row items-center justify-between px-2 py-3">
        <div className="flex items-center gap-6">
          <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight first:mt-0 pl-4">
            Bokul
          </h2>
          <div className="h-6 w-px bg-border"></div>
          <nav className="flex gap-2 text-lg">
            {links.map(({ to, label, icon: Icon }) => {
              const active = isActive(to);
              return (
                <Button
                  key={to}
                  variant={active ? "default" : "ghost"}
                  className={cn(
                    "text-sm gap-2",
                    active && "bg-primary text-primary-foreground"
                  )}
                  asChild
                >
                  <Link to={to}>
                    <Icon className="h-4 w-4" />
                    {label}
                  </Link>
                </Button>
              );
            })}
          </nav>
        </div>
        <div className="flex items-center gap-2 px-4">
          <ModeToggle />
          <UserMenu />
        </div>
      </div>
      <hr />
    </div>
  );
}
